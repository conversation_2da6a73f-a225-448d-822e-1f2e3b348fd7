import 'package:get/get.dart';
import 'package:zero_koin/models/notification_model.dart';
import 'package:zero_koin/services/api_service.dart';

class NotificationController extends GetxController {
  // Observable list of notifications
  final RxList<NotificationModel> notifications = <NotificationModel>[].obs;
  
  // Loading state
  final RxBool isLoading = false.obs;
  
  // Error state
  final RxString error = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // Fetch notifications when controller is initialized
    fetchNotifications();
  }

  // Fetch all notifications from backend
  Future<void> fetchNotifications() async {
    try {
      isLoading.value = true;
      error.value = '';
      
      print('🔄 Fetching notifications from backend...');
      
      final response = await ApiService.getAllNotifications();
      
      if (response != null && response['notifications'] != null) {
        final List<dynamic> notificationData = response['notifications'];
        
        // Convert to NotificationModel objects
        final List<NotificationModel> fetchedNotifications = notificationData
            .map((json) => NotificationModel.fromJson(json))
            .toList();
        
        notifications.value = fetchedNotifications;
        
        print('✅ Successfully fetched ${fetchedNotifications.length} notifications');
      } else {
        print('⚠️ No notifications data received from backend');
        notifications.value = [];
      }
    } catch (e) {
      print('❌ Error fetching notifications: $e');
      error.value = 'Failed to load notifications: $e';
      notifications.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  // Refresh notifications (pull to refresh)
  Future<void> refreshNotifications() async {
    await fetchNotifications();
  }

  // Get recent notifications (last 10)
  List<NotificationModel> get recentNotifications {
    return notifications.take(10).toList();
  }

  // Get sent notifications only
  List<NotificationModel> get sentNotifications {
    return notifications.where((notification) => notification.isSent).toList();
  }

  // Get unsent notifications only
  List<NotificationModel> get unsentNotifications {
    return notifications.where((notification) => !notification.isSent).toList();
  }

  // Check if there are any notifications
  bool get hasNotifications {
    return notifications.isNotEmpty;
  }

  // Get notification count
  int get notificationCount {
    return notifications.length;
  }

  // Get recent notification count
  int get recentNotificationCount {
    return recentNotifications.length;
  }

  // Clear error
  void clearError() {
    error.value = '';
  }

  // Handle notification tap (for future use)
  void onNotificationTap(NotificationModel notification) {
    print('Notification tapped: ${notification.title}');
    // Add navigation logic here if needed
  }
}
